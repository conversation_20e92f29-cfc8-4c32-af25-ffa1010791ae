add_action('rest_api_init', function () {

  // ——————————————————————————————————————————————
// 1) GET /wp-json/api/v1/students-coach
register_rest_route('api/v1', '/students-coach', [
  'methods'  => 'GET',
  'callback' => 'as_list_students_with_coach',
  'permission_callback' => '__return_true',
]);

  // ——————————————————————————————————————————————
  // 2) POST /wp-json/api/v1/student-coach
  //    Body JSON: { "id": 123, "coach_id": 456 }
  //    Actualiza el meta coach_preferido del estudiante
  register_rest_route('api/v1', '/student-coach', [
    'methods'             => 'POST',
    'callback'            => 'as_update_student_coach',
    'permission_callback' => '__return_true',
    'args'                => [
      'id' => [
        'required'          => true,
        'validate_callback' => function($v){ return ctype_digit((string)$v); }
      ],
      'coach_id' => [
        'required'          => true,
        'validate_callback' => function($v){ return ctype_digit((string)$v); }
      ],
    ],
  ]);

});


function as_list_students_with_coach( \WP_REST_Request $req ) {
  global $wpdb;

  // 1-A) Listamos alumnos WP
  $students_wp = get_users([
    'role__in' => ['student','subscriber','customer'],
    'orderby'  => 'display_name',
  ]);
  $out_students = [];
  foreach ( $students_wp as $u ) {
    // siempre entero o null
    $meta = get_user_meta( $u->ID, 'coach_preferido', true );
    $coach_id = ( $meta !== '' && ctype_digit((string)$meta) )
              ? intval($meta)
              : null;

    $out_students[] = [
      'id'    => $u->ID,
      'name'  => $u->display_name,
      'email' => $u->user_email,
      'coach' => $coach_id,
    ];
  }

	
	/// ESTA PARTE SOLO ES PARA EDITAR
  // 1-B) Listamos **Amelia providers** por su tabla, no por WP roles
  $providers = $wpdb->get_results("
    SELECT id, firstName, lastName
      FROM qxq_amelia_users
     WHERE type = 'provider'
  ", ARRAY_A);

  $out_coaches = [];
  foreach( $providers as $p ){
    $id   = intval( $p['id'] );
    $name = trim( $p['firstName'].' '.$p['lastName'] );
    $out_coaches[ $id ] = $name;
  }

  return new WP_REST_Response([
    'students' => $out_students,
    'coaches'  => $out_coaches,
  ], 200 );
}





/**
 * 2) Callback para actualizar coach_preferido
 */
function as_update_student_coach( \WP_REST_Request $req ) {
  $body = json_decode( $req->get_body(), true );

  $user_id  = intval( $body['id'] );
  $coach_id = intval( $body['coach_id'] );

  if ( ! get_user_by( 'ID', $user_id ) ) {
    return new WP_REST_Response( ['error'=>'Estudiante no encontrado'], 404 );
  }

  // Guarda el meta
  update_user_meta( $user_id, 'coach_preferido', $coach_id );

  return new WP_REST_Response( ['success'=>true], 200 );
}
